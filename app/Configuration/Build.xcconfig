// The `DISAMBIGUATOR` configuration is to make it easier to build
// and run a sample code project. Once you set your project's development team,
// you'll have a unique bundle identifier. This is because the bundle identifier
// is derived based on the 'DISAMBIGUATOR' value. Do not use this
// approach in your own projects—it's only useful for example projects because
// they are frequently downloaded and don't have a development team set.
DISAMBIGUATOR=${DEVELOPMENT_TEAM}
